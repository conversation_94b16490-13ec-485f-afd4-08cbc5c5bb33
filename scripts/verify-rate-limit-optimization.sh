#!/bin/bash

# AI服务速率限制优化验证脚本
# 用于快速检查所有优化是否正确实施

echo "🚀 开始验证AI服务速率限制优化..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_file() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $description${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

check_content() {
    local file=$1
    local pattern=$2
    local description=$3
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ] && grep -q "$pattern" "$file"; then
        echo -e "${GREEN}✅ $description${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo -e "${BLUE}📁 检查核心文件是否存在...${NC}"
echo "--------------------------------------------------"

# 检查核心文件
check_file "src/main/java/com/whiskerguard/ai/service/util/RateLimitHandler.java" "RateLimitHandler核心类"
check_file "src/main/java/com/whiskerguard/ai/config/RateLimitConfig.java" "RateLimitConfig配置类"
check_file "src/test/java/com/whiskerguard/ai/service/util/RateLimitHandlerTest.java" "RateLimitHandler单元测试"
check_file "src/test/java/com/whiskerguard/ai/service/invocation/RateLimitOptimizationIntegrationTest.java" "集成测试"
check_file "docs/ai-rate-limit-optimization.md" "优化文档"

echo ""
echo -e "${BLUE}🔧 检查Invoker优化情况...${NC}"
echo "--------------------------------------------------"

# 检查所有Invoker是否已集成RateLimitHandler
INVOKERS=("KimiInvoker" "DouBaoInvoker" "ClaudeInvoker" "QwenInvoker" "DeepSeekInvoker" "ChatGptInvoker" "LaWGPTInvoker" "ChatLawInvoker")

for invoker in "${INVOKERS[@]}"; do
    file="src/main/java/com/whiskerguard/ai/service/invocation/${invoker}.java"
    check_content "$file" "RateLimitHandler" "${invoker} 已集成 RateLimitHandler"
    check_content "$file" "rateLimitHandler.shouldDelayCall" "${invoker} 已实现调用前检查"
    check_content "$file" "rateLimitHandler.recordSuccessfulCall" "${invoker} 已实现成功记录"
    check_content "$file" "rateLimitHandler.isRateLimitError" "${invoker} 已实现错误检测"
done

echo ""
echo -e "${BLUE}⚙️ 检查配置文件...${NC}"
echo "--------------------------------------------------"

# 检查配置文件
check_content "src/main/resources/config/application.yml" "java.lang.RuntimeException" "主配置文件包含RuntimeException重试配置"
check_content "src/main/resources/config/application-dev.yml" "RateLimitHandler" "开发环境配置已更新"
check_content "src/main/resources/config/application-prod.yml" "RateLimitHandler" "生产环境配置已更新"

# 检查所有API的重试配置
APIS=("kimi" "doubao" "claude" "qwen" "deepseek" "chatgpt" "lawgpt" "chatlaw")

for api in "${APIS[@]}"; do
    check_content "src/main/resources/config/application.yml" "$api:" "主配置包含 $api 重试配置"
done

echo ""
echo -e "${BLUE}🧪 运行编译测试...${NC}"
echo "--------------------------------------------------"

# 编译测试
echo "正在编译项目..."
if ./mvnw compile -Dmaven.test.skip=true -q > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 项目编译成功${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ 项目编译失败${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo -e "${BLUE}📊 生成验证报告...${NC}"
echo "=================================================="

# 计算成功率
SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo "总检查项目: $TOTAL_CHECKS"
echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
echo -e "成功率: ${GREEN}$SUCCESS_RATE%${NC}"

echo ""
if [ $SUCCESS_RATE -ge 95 ]; then
    echo -e "${GREEN}🎉 优化验证成功！所有关键组件都已正确实施。${NC}"
    echo -e "${GREEN}✨ 您的AI服务现在具备了智能速率限制处理能力！${NC}"
    echo ""
    echo -e "${BLUE}📋 下一步建议：${NC}"
    echo "1. 部署到测试环境进行实际测试"
    echo "2. 监控429错误率的变化"
    echo "3. 根据实际使用情况调优参数"
    echo "4. 设置监控告警"
    exit 0
elif [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "${YELLOW}⚠️ 优化基本完成，但有一些小问题需要修复。${NC}"
    echo -e "${YELLOW}请检查上述失败的项目并进行修复。${NC}"
    exit 1
else
    echo -e "${RED}❌ 优化验证失败！存在重大问题需要解决。${NC}"
    echo -e "${RED}请仔细检查所有失败的项目并重新实施优化。${NC}"
    exit 2
fi
