package com.whiskerguard.ai.service.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RateLimitHandler 单元测试
 * 
 * <AUTHOR> Guard AI Team
 */
class RateLimitHandlerTest {

    private RateLimitHandler rateLimitHandler;

    @BeforeEach
    void setUp() {
        rateLimitHandler = new RateLimitHandler();
    }

    @Test
    void testIsRateLimitError() {
        // 测试429错误识别
        Exception rateLimitException = new RuntimeException("429 Too Many Requests");
        assertTrue(rateLimitHandler.isRateLimitError(rateLimitException));

        // 测试WebClientResponseException.TooManyRequests
        WebClientResponseException.TooManyRequests tooManyRequests = 
            new WebClientResponseException.TooManyRequests("Too Many Requests", null, null, null, null);
        assertTrue(rateLimitHandler.isRateLimitError(tooManyRequests));

        // 测试其他错误
        Exception otherException = new RuntimeException("500 Internal Server Error");
        assertFalse(rateLimitHandler.isRateLimitError(otherException));

        // 测试null
        assertFalse(rateLimitHandler.isRateLimitError(null));
    }

    @Test
    void testCalculateBackoffTime() {
        String apiKey = "kimi";
        
        // 测试第一次重试
        long backoff1 = rateLimitHandler.calculateBackoffTime(apiKey, 1);
        assertTrue(backoff1 >= 4000 && backoff1 <= 6000); // 5000ms ± 20%
        
        // 测试第二次重试（应该更长）
        long backoff2 = rateLimitHandler.calculateBackoffTime(apiKey, 2);
        assertTrue(backoff2 > backoff1);
        
        // 测试第三次重试
        long backoff3 = rateLimitHandler.calculateBackoffTime(apiKey, 3);
        assertTrue(backoff3 > backoff2);
        
        // 测试最大退避时间限制
        long backoff10 = rateLimitHandler.calculateBackoffTime(apiKey, 10);
        assertTrue(backoff10 <= 60000); // 不超过60秒
    }

    @Test
    void testRecordRateLimitError() {
        String apiKey = "kimi";
        
        // 记录第一次错误
        rateLimitHandler.recordRateLimitError(apiKey);
        long backoff1 = rateLimitHandler.calculateBackoffTime(apiKey, 1);
        
        // 记录第二次错误
        rateLimitHandler.recordRateLimitError(apiKey);
        long backoff2 = rateLimitHandler.calculateBackoffTime(apiKey, 1);
        
        // 连续错误应该增加退避时间
        assertTrue(backoff2 > backoff1);
    }

    @Test
    void testRecordSuccessfulCall() {
        String apiKey = "kimi";
        
        // 记录错误
        rateLimitHandler.recordRateLimitError(apiKey);
        long backoffWithError = rateLimitHandler.calculateBackoffTime(apiKey, 1);
        
        // 记录成功调用（应该重置错误计数）
        rateLimitHandler.recordSuccessfulCall(apiKey);
        long backoffAfterSuccess = rateLimitHandler.calculateBackoffTime(apiKey, 1);
        
        // 成功调用后退避时间应该减少
        assertTrue(backoffAfterSuccess < backoffWithError);
    }

    @Test
    void testShouldDelayCall() {
        String apiKey = "kimi";
        
        // 第一次调用不应该延迟
        long delay1 = rateLimitHandler.shouldDelayCall(apiKey);
        assertEquals(0, delay1);
        
        // 记录成功调用
        rateLimitHandler.recordSuccessfulCall(apiKey);
        
        // 立即再次调用应该需要延迟
        long delay2 = rateLimitHandler.shouldDelayCall(apiKey);
        assertTrue(delay2 > 0);
    }

    @Test
    void testGetErrorAdvice() {
        String apiKey = "kimi";
        Exception rateLimitException = new RuntimeException("429 Too Many Requests");
        
        String advice = rateLimitHandler.getErrorAdvice(apiKey, rateLimitException);
        
        assertNotNull(advice);
        assertTrue(advice.contains("429"));
        assertTrue(advice.contains("速率限制"));
        assertTrue(advice.contains("重试"));
    }

    @Test
    void testDifferentApiKeys() {
        // 测试不同API的基础退避时间
        long kimiBackoff = rateLimitHandler.calculateBackoffTime("kimi", 1);
        long doubaoBackoff = rateLimitHandler.calculateBackoffTime("doubao", 1);
        
        // Kimi的基础退避时间应该更长
        assertTrue(kimiBackoff > doubaoBackoff);
    }

    @Test
    void testCleanupExpiredRecords() {
        String apiKey = "test";
        
        // 记录一些数据
        rateLimitHandler.recordSuccessfulCall(apiKey);
        rateLimitHandler.recordRateLimitError(apiKey);
        
        // 清理操作不应该抛出异常
        assertDoesNotThrow(() -> rateLimitHandler.cleanupExpiredRecords());
    }
}