package com.whiskerguard.ai.service.agent.core.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AgentConfig;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.repository.AgentConfigRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.core.AgentConfigCoreService;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Agent配置核心服务实现
 * Agent Configuration Core Service Implementation
 *
 * 提供Agent配置管理功能的完整实现
 * Provide complete implementation of Agent configuration management functions
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class AgentConfigCoreServiceImpl implements AgentConfigCoreService {

    private static final Logger log = LoggerFactory.getLogger(AgentConfigCoreServiceImpl.class);

    private final AgentConfigRepository agentConfigRepository;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数注入依赖
     * Constructor dependency injection
     */
    public AgentConfigCoreServiceImpl(
        AgentConfigRepository agentConfigRepository,
        ObjectMapper objectMapper
    ) {
        this.agentConfigRepository = agentConfigRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "agent-config", key = "#key + ':' + #tenantId + ':' + #taskType")
    public <T> Optional<T> getConfig(String key, Class<T> valueType, Long tenantId, AgentTaskType taskType) {
        log.debug("获取配置 - 键: {}, 类型: {}, 租户: {}, 任务类型: {} / Getting config - Key: {}, Type: {}, Tenant: {}, Task type: {}",
            key, valueType.getSimpleName(), tenantId, taskType, key, valueType.getSimpleName(), tenantId, taskType);

        try {
            // 按优先级查找配置：具体配置 > 租户配置 > 全局配置
            // Find configuration by priority: specific config > tenant config > global config
            List<AgentConfig> configs = agentConfigRepository.findByConfigKeyAndTenantAndTaskType(key, tenantId, taskType);

            if (configs.isEmpty()) {
                log.debug("未找到配置 - 键: {} / Config not found - Key: {}", key, key);
                return Optional.empty();
            }

            // 使用第一个（优先级最高的）配置
            // Use the first (highest priority) configuration
            AgentConfig config = configs.get(0);
            String configValue = config.getConfigValue();

            // 类型转换
            // Type conversion
            T convertedValue = convertValue(configValue, valueType);

            log.debug("成功获取配置 - 键: {}, 值: {} / Successfully got config - Key: {}, Value: {}",
                key, convertedValue, key, convertedValue);

            return Optional.of(convertedValue);

        } catch (Exception e) {
            log.error("获取配置失败 - 键: {} / Failed to get config - Key: {}", key, key, e);
            return Optional.empty();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public <T> T getConfig(String key, T defaultValue, Long tenantId, AgentTaskType taskType) {
        @SuppressWarnings("unchecked")
        Class<T> valueType = (Class<T>) defaultValue.getClass();

        return getConfig(key, valueType, tenantId, taskType)
            .orElse(defaultValue);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<String> getStringConfig(String key, Long tenantId, AgentTaskType taskType) {
        return getConfig(key, String.class, tenantId, taskType);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Integer> getIntegerConfig(String key, Long tenantId, AgentTaskType taskType) {
        return getConfig(key, Integer.class, tenantId, taskType);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Boolean> getBooleanConfig(String key, Long tenantId, AgentTaskType taskType) {
        return getConfig(key, Boolean.class, tenantId, taskType);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Double> getDoubleConfig(String key, Long tenantId, AgentTaskType taskType) {
        return getConfig(key, Double.class, tenantId, taskType);
    }

    @Override
    @CacheEvict(value = "agent-config", key = "#key + ':' + #tenantId + ':' + #taskType")
    public AgentConfig setConfig(String key, Object value, Long tenantId, AgentTaskType taskType,
                                String configType, String configGroup, String description, Integer priority) {
        log.debug("设置配置 - 键: {}, 值: {}, 租户: {}, 任务类型: {} / Setting config - Key: {}, Value: {}, Tenant: {}, Task type: {}",
            key, value, tenantId, taskType, key, value, tenantId, taskType);

        try {
            // 查找现有配置
            // Find existing configuration
            Optional<AgentConfig> existingConfig = findExactConfig(key, tenantId, taskType);

            AgentConfig config;
            if (existingConfig.isPresent()) {
                // 更新现有配置
                // Update existing configuration
                config = existingConfig.get();
                config.setConfigValue(convertToString(value));
                config.setConfigType(configType);
                config.setConfigGroup(configGroup);
                config.setDescription(description);
                config.setPriority(priority);
                updateAuditFields(config);
            } else {
                // 创建新配置
                // Create new configuration
                config = new AgentConfig();
                config.setConfigKey(key);
                config.setConfigValue(convertToString(value));
                config.setTenantId(tenantId);
                config.setTaskType(taskType);
                config.setConfigType(configType);
                config.setConfigGroup(configGroup);
                config.setDescription(description);
                config.setPriority(priority != null ? priority : 0);
                config.setEnabled(true);
                config.setIsDeleted(false);
                config.setVersion(1);
                setAuditFields(config);
            }

            AgentConfig savedConfig = agentConfigRepository.save(config);

            log.info("配置保存成功 - ID: {}, 键: {} / Config saved successfully - ID: {}, Key: {}",
                savedConfig.getId(), key, savedConfig.getId(), key);

            return savedConfig;

        } catch (Exception e) {
            log.error("保存配置失败 - 键: {} / Failed to save config - Key: {}", key, key, e);
            throw new RuntimeException("保存配置失败: " + e.getMessage(), e);
        }
    }

    @Override
    public AgentConfig setConfig(String key, Object value, Long tenantId, AgentTaskType taskType) {
        return setConfig(key, value, tenantId, taskType, null, null, null, null);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasConfig(String key, Long tenantId, AgentTaskType taskType) {
        return agentConfigRepository.existsByConfigKeyAndTenantAndTaskType(key, tenantId, taskType);
    }

    @Override
    @CacheEvict(value = "agent-config", key = "#key + ':' + #tenantId + ':' + #taskType")
    public boolean removeConfig(String key, Long tenantId, AgentTaskType taskType) {
        log.debug("删除配置 - 键: {}, 租户: {}, 任务类型: {} / Removing config - Key: {}, Tenant: {}, Task type: {}",
            key, tenantId, taskType, key, tenantId, taskType);

        try {
            Optional<AgentConfig> config = findExactConfig(key, tenantId, taskType);
            if (config.isPresent()) {
                // 软删除
                // Soft delete
                AgentConfig configEntity = config.get();
                configEntity.setIsDeleted(true);
                updateAuditFields(configEntity);
                agentConfigRepository.save(configEntity);

                log.info("配置删除成功 - 键: {} / Config removed successfully - Key: {}", key, key);
                return true;
            } else {
                log.warn("要删除的配置不存在 - 键: {} / Config to remove not found - Key: {}", key, key);
                return false;
            }

        } catch (Exception e) {
            log.error("删除配置失败 - 键: {} / Failed to remove config - Key: {}", key, key, e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "agent-config-tenant", key = "#tenantId")
    public List<AgentConfig> getTenantConfigs(Long tenantId) {
        log.debug("获取租户配置 - 租户: {} / Getting tenant configs - Tenant: {}", tenantId, tenantId);
        return agentConfigRepository.findAllByTenant(tenantId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentConfig> getConfigsByGroup(String configGroup, Long tenantId) {
        log.debug("根据配置组获取配置 - 组: {}, 租户: {} / Getting configs by group - Group: {}, Tenant: {}",
            configGroup, tenantId, configGroup, tenantId);
        return agentConfigRepository.findByConfigGroupAndTenant(configGroup, tenantId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AgentConfig> getConfigsByType(String configType, Long tenantId) {
        log.debug("根据配置类型获取配置 - 类型: {}, 租户: {} / Getting configs by type - Type: {}, Tenant: {}",
            configType, tenantId, configType, tenantId);
        return agentConfigRepository.findByConfigTypeAndTenant(configType, tenantId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getBatchConfigs(List<String> keys, Long tenantId, AgentTaskType taskType) {
        log.debug("批量获取配置 - 键数量: {}, 租户: {}, 任务类型: {} / Batch getting configs - Key count: {}, Tenant: {}, Task type: {}",
            keys.size(), tenantId, taskType, keys.size(), tenantId, taskType);

        return keys.stream()
            .collect(Collectors.toMap(
                key -> key,
                key -> getConfig(key, Object.class, tenantId, taskType).orElse(null),
                (existing, replacement) -> existing,
                HashMap::new
            ));
    }

    @Override
    @CacheEvict(value = {"agent-config", "agent-config-tenant"}, allEntries = true)
    public boolean enableConfig(Long configId) {
        return updateConfigEnabled(configId, true);
    }

    @Override
    @CacheEvict(value = {"agent-config", "agent-config-tenant"}, allEntries = true)
    public boolean disableConfig(Long configId) {
        return updateConfigEnabled(configId, false);
    }

    @Override
    @CacheEvict(value = {"agent-config", "agent-config-tenant"}, allEntries = true)
    public boolean updateConfigPriority(Long configId, Integer priority) {
        log.debug("更新配置优先级 - ID: {}, 优先级: {} / Updating config priority - ID: {}, Priority: {}",
            configId, priority, configId, priority);

        try {
            Optional<AgentConfig> configOpt = agentConfigRepository.findById(configId);
            if (configOpt.isPresent()) {
                AgentConfig config = configOpt.get();
                config.setPriority(priority);
                updateAuditFields(config);
                agentConfigRepository.save(config);

                log.info("配置优先级更新成功 - ID: {} / Config priority updated successfully - ID: {}", configId, configId);
                return true;
            } else {
                log.warn("配置不存在 - ID: {} / Config not found - ID: {}", configId, configId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新配置优先级失败 - ID: {} / Failed to update config priority - ID: {}", configId, configId, e);
            return false;
        }
    }

    @Override
    @CacheEvict(value = {"agent-config", "agent-config-tenant"}, allEntries = true)
    public void clearConfigCache(Long tenantId) {
        log.debug("清除配置缓存 - 租户: {} / Clearing config cache - Tenant: {}", tenantId, tenantId);
        // 缓存清除由注解自动处理
        // Cache clearing is handled automatically by annotations
    }

    /**
     * 查找精确匹配的配置
     * Find exact matching configuration
     */
    private Optional<AgentConfig> findExactConfig(String key, Long tenantId, AgentTaskType taskType) {
        if (tenantId != null && taskType != null) {
            return agentConfigRepository.findByConfigKeyAndTenantIdAndTaskTypeAndIsDeletedFalse(key, tenantId, taskType);
        } else if (tenantId != null) {
            return agentConfigRepository.findByConfigKeyAndTenantIdAndTaskTypeIsNullAndIsDeletedFalse(key, tenantId);
        } else {
            return agentConfigRepository.findByConfigKeyAndTenantIdIsNullAndTaskTypeIsNullAndIsDeletedFalse(key);
        }
    }

    /**
     * 更新配置启用状态
     * Update configuration enabled status
     */
    private boolean updateConfigEnabled(Long configId, boolean enabled) {
        log.debug("更新配置启用状态 - ID: {}, 启用: {} / Updating config enabled status - ID: {}, Enabled: {}",
            configId, enabled, configId, enabled);

        try {
            Optional<AgentConfig> configOpt = agentConfigRepository.findById(configId);
            if (configOpt.isPresent()) {
                AgentConfig config = configOpt.get();
                config.setEnabled(enabled);
                updateAuditFields(config);
                agentConfigRepository.save(config);

                log.info("配置启用状态更新成功 - ID: {}, 启用: {} / Config enabled status updated successfully - ID: {}, Enabled: {}",
                    configId, enabled, configId, enabled);
                return true;
            } else {
                log.warn("配置不存在 - ID: {} / Config not found - ID: {}", configId, configId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新配置启用状态失败 - ID: {} / Failed to update config enabled status - ID: {}", configId, configId, e);
            return false;
        }
    }

    /**
     * 值类型转换
     * Value type conversion
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(String value, Class<T> targetType) {
        if (value == null) {
            return null;
        }

        if (targetType == String.class) {
            return (T) value;
        } else if (targetType == Integer.class) {
            return (T) Integer.valueOf(value);
        } else if (targetType == Long.class) {
            return (T) Long.valueOf(value);
        } else if (targetType == Boolean.class) {
            return (T) Boolean.valueOf(value);
        } else if (targetType == Double.class) {
            return (T) Double.valueOf(value);
        } else if (targetType == Float.class) {
            return (T) Float.valueOf(value);
        } else {
            // 对于复杂对象，尝试JSON反序列化
            // For complex objects, try JSON deserialization
            try {
                return objectMapper.readValue(value, targetType);
            } catch (JsonProcessingException e) {
                throw new IllegalArgumentException("无法转换配置值: " + value + " 到类型: " + targetType.getSimpleName(), e);
            }
        }
    }

    /**
     * 将值转换为字符串
     * Convert value to string
     */
    private String convertToString(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return (String) value;
        } else if (value instanceof Number || value instanceof Boolean) {
            return value.toString();
        } else {
            // 对于复杂对象，序列化为JSON
            // For complex objects, serialize to JSON
            try {
                return objectMapper.writeValueAsString(value);
            } catch (JsonProcessingException e) {
                throw new IllegalArgumentException("无法序列化配置值: " + value, e);
            }
        }
    }

    /**
     * 设置审计字段
     * Set audit fields
     */
    private void setAuditFields(AgentConfig config) {
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        Instant now = Instant.now();
        config.setCreatedBy(currentUser);
        config.setCreatedAt(now);
        config.setUpdatedBy(currentUser);
        config.setUpdatedAt(now);
    }

    /**
     * 更新审计字段
     * Update audit fields
     */
    private void updateAuditFields(AgentConfig config) {
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        config.setUpdatedBy(currentUser);
        config.setUpdatedAt(Instant.now());
        config.setVersion(config.getVersion() + 1);
    }
}
