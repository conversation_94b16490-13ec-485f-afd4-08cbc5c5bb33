package com.whiskerguard.ai.config;

import java.net.URI;
import java.util.concurrent.TimeUnit;
import javax.cache.configuration.MutableConfiguration;
import javax.cache.expiry.CreatedExpiryPolicy;
import javax.cache.expiry.Duration;
import javax.cache.spi.CachingProvider;
import javax.cache.Caching;
import org.hibernate.cache.jcache.ConfigSettings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.jcache.configuration.RedissonConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

@Configuration
@EnableCaching
public class CacheConfiguration {

    private GitProperties gitProperties;
    private BuildProperties buildProperties;

    @Bean
    public RedissonClient redissonClient(JHipsterProperties jHipsterProperties) {
        Config config = new Config();

        URI redisUri = URI.create(jHipsterProperties.getCache().getRedis().getServer()[0]);

        // Fix Hibernate lazy initialization https://github.com/jhipster/generator-jhipster/issues/22889
        config.setCodec(new org.redisson.codec.SerializationCodec());

        if (jHipsterProperties.getCache().getRedis().isCluster()) {
            ClusterServersConfig clusterServersConfig = config
                .useClusterServers()
                .setMasterConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .addNodeAddress(jHipsterProperties.getCache().getRedis().getServer());

            if (redisUri.getUserInfo() != null) {
                clusterServersConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        } else {
            SingleServerConfig singleServerConfig = config
                .useSingleServer()
                .setConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .setAddress(jHipsterProperties.getCache().getRedis().getServer()[0]);

            if (redisUri.getUserInfo() != null) {
                singleServerConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        }

        return Redisson.create(config);
    }

    @Bean
    @ConditionalOnProperty(name = "spring.cache.type", havingValue = "jcache")
    public javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration(
        JHipsterProperties jHipsterProperties,
        RedissonClient redissonClient
    ) {
        MutableConfiguration<Object, Object> jcacheConfig = new MutableConfiguration<>();
        jcacheConfig.setStatisticsEnabled(true);
        jcacheConfig.setExpiryPolicyFactory(
            CreatedExpiryPolicy.factoryOf(new Duration(TimeUnit.SECONDS, jHipsterProperties.getCache().getRedis().getExpiration()))
        );
        return RedissonConfiguration.fromInstance(redissonClient, jcacheConfig);
    }

    @Bean
    @ConditionalOnProperty(name = "spring.cache.type", havingValue = "jcache")
    public javax.cache.CacheManager cacheManager(javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration) {
        CachingProvider cachingProvider = Caching.getCachingProvider("org.redisson.jcache.JCachingProvider");
        javax.cache.CacheManager cacheManager = cachingProvider.getCacheManager();
        return cacheManager;
    }

    @Bean
    @ConditionalOnProperty(name = "spring.cache.type", havingValue = "jcache")
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cacheManager) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cacheManager);
    }

    @Bean
    @ConditionalOnProperty(name = "spring.cache.type", havingValue = "jcache")
    public JCacheManagerCustomizer cacheManagerCustomizer(javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration) {
        return cm -> {
            createCache(cm, com.whiskerguard.ai.domain.AiTool.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AiRequest.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AiReview.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AiToolMetrics.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractReview.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractReview.class.getName() + ".contractParties", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractReview.class.getName() + ".contractRiskPoints", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractReview.class.getName() + ".contractClauseIssues", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractParty.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractRiskPoint.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.ContractClauseIssue.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PromptTemplate.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PromptTemplate.class.getName() + ".promptTemplateVariables", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PromptTemplate.class.getName() + ".promptTemplateVersions", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PromptTemplateVariable.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PromptTemplateVersion.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.TenantPromptConfig.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AgentTask.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AgentTask.class.getName() + ".taskSteps", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AgentTask.class.getName() + ".contexts", jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.TaskStep.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AgentContext.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.KnowledgeCache.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.AgentConfig.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PopularQa.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.ai.domain.PopularQaClickLog.class.getName(), jcacheConfiguration);
            // Agent配置相关的自定义缓存 / Agent configuration related custom caches
            createCache(cm, "agent-config", jcacheConfiguration);
            createCache(cm, "agent-config-tenant", jcacheConfiguration);
            // jhipster-needle-redis-add-entry
        };
    }

    private void createCache(
        javax.cache.CacheManager cm,
        String cacheName,
        javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration
    ) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }
}
