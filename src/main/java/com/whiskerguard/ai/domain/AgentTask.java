package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.AgentTaskStatus;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

/**
 * Agent任务实体
 * 记录Agent执行的任务信息
 */
@Entity
@Table(name = "agent_task")
// @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)  // 临时注释掉缓存注解
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AgentTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 任务类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = false)
    private AgentTaskType taskType;

    /**
     * 任务标题
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "title", length = 200, nullable = false)
    private String title;

    /**
     * 任务描述
     */
    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 任务状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AgentTaskStatus status;

    /**
     * 任务优先级
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false)
    private TaskPriority priority;

    /**
     * 请求数据
     */
    @Lob
    @Column(name = "request_data", columnDefinition = "LONGTEXT")
    private String requestData;

    /**
     * 响应数据
     */
    @Lob
    @Column(name = "response_data", columnDefinition = "LONGTEXT")
    private String responseData;

    /**
     * 错误信息
     */
    @Size(max = 2000)
    @Column(name = "error_message", length = 2000)
    private String errorMessage;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Instant endTime;

    /**
     * 执行时长(毫秒)
     */
    @Column(name = "execution_time")
    private Long executionTime;

    /**
     * 进度百分比
     */
    @Min(value = 0)
    @Max(value = 100)
    @Column(name = "progress")
    private Integer progress;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    /**
     * 一个Agent任务可以有多个执行步骤
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "agentTask")
    @JsonIgnoreProperties(value = { "agentTask" }, allowSetters = true)
    private Set<TaskStep> taskSteps = new HashSet<>();

    /**
     * 一个Agent任务可以有多个上下文
     */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "agentTask")
    @JsonIgnoreProperties(value = { "agentTask" }, allowSetters = true)
    private Set<AgentContext> contexts = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AgentTask id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public AgentTask tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public AgentTaskType getTaskType() {
        return this.taskType;
    }

    public AgentTask taskType(AgentTaskType taskType) {
        this.setTaskType(taskType);
        return this;
    }

    public void setTaskType(AgentTaskType taskType) {
        this.taskType = taskType;
    }

    public String getTitle() {
        return this.title;
    }

    public AgentTask title(String title) {
        this.setTitle(title);
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return this.description;
    }

    public AgentTask description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public AgentTaskStatus getStatus() {
        return this.status;
    }

    public AgentTask status(AgentTaskStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(AgentTaskStatus status) {
        this.status = status;
    }

    public TaskPriority getPriority() {
        return this.priority;
    }

    public AgentTask priority(TaskPriority priority) {
        this.setPriority(priority);
        return this;
    }

    public void setPriority(TaskPriority priority) {
        this.priority = priority;
    }

    public String getRequestData() {
        return this.requestData;
    }

    public AgentTask requestData(String requestData) {
        this.setRequestData(requestData);
        return this;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public String getResponseData() {
        return this.responseData;
    }

    public AgentTask responseData(String responseData) {
        this.setResponseData(responseData);
        return this;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public AgentTask errorMessage(String errorMessage) {
        this.setErrorMessage(errorMessage);
        return this;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getStartTime() {
        return this.startTime;
    }

    public AgentTask startTime(Instant startTime) {
        this.setStartTime(startTime);
        return this;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return this.endTime;
    }

    public AgentTask endTime(Instant endTime) {
        this.setEndTime(endTime);
        return this;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public Long getExecutionTime() {
        return this.executionTime;
    }

    public AgentTask executionTime(Long executionTime) {
        this.setExecutionTime(executionTime);
        return this;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public Integer getProgress() {
        return this.progress;
    }

    public AgentTask progress(Integer progress) {
        this.setProgress(progress);
        return this;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public AgentTask metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AgentTask version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AgentTask createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AgentTask createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AgentTask updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AgentTask updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AgentTask isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Set<TaskStep> getTaskSteps() {
        return this.taskSteps;
    }

    public void setTaskSteps(Set<TaskStep> taskSteps) {
        if (this.taskSteps != null) {
            this.taskSteps.forEach(i -> i.setAgentTask(null));
        }
        if (taskSteps != null) {
            taskSteps.forEach(i -> i.setAgentTask(this));
        }
        this.taskSteps = taskSteps;
    }

    public AgentTask taskSteps(Set<TaskStep> taskSteps) {
        this.setTaskSteps(taskSteps);
        return this;
    }

    public AgentTask addTaskSteps(TaskStep taskStep) {
        this.taskSteps.add(taskStep);
        taskStep.setAgentTask(this);
        return this;
    }

    public AgentTask removeTaskSteps(TaskStep taskStep) {
        this.taskSteps.remove(taskStep);
        taskStep.setAgentTask(null);
        return this;
    }

    public Set<AgentContext> getContexts() {
        return this.contexts;
    }

    public void setContexts(Set<AgentContext> agentContexts) {
        if (this.contexts != null) {
            this.contexts.forEach(i -> i.setAgentTask(null));
        }
        if (agentContexts != null) {
            agentContexts.forEach(i -> i.setAgentTask(this));
        }
        this.contexts = agentContexts;
    }

    public AgentTask contexts(Set<AgentContext> agentContexts) {
        this.setContexts(agentContexts);
        return this;
    }

    public AgentTask addContexts(AgentContext agentContext) {
        this.contexts.add(agentContext);
        agentContext.setAgentTask(this);
        return this;
    }

    public AgentTask removeContexts(AgentContext agentContext) {
        this.contexts.remove(agentContext);
        agentContext.setAgentTask(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AgentTask)) {
            return false;
        }
        return getId() != null && getId().equals(((AgentTask) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AgentTask{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", taskType='" + getTaskType() + "'" +
            ", title='" + getTitle() + "'" +
            ", description='" + getDescription() + "'" +
            ", status='" + getStatus() + "'" +
            ", priority='" + getPriority() + "'" +
            ", requestData='" + getRequestData() + "'" +
            ", responseData='" + getResponseData() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", startTime='" + getStartTime() + "'" +
            ", endTime='" + getEndTime() + "'" +
            ", executionTime=" + getExecutionTime() +
            ", progress=" + getProgress() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
