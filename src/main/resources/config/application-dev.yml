# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.whiskerguard.ai: DEBUG

management:
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  cloud:
    consul:
      config:
        fail-fast: false # if not in "prod" profile, do not force to use Spring Cloud Config
        format: yaml
        profile-separator: '-'
      discovery:
        prefer-ip-address: true
        tags:
          - profile=${spring.profiles.active}
          - version='@project.version@'
          - git-version=${git.commit.id.describe:}
          - git-commit=${git.commit.id.abbrev:}
          - git-branch=${git.branch:}
      host: localhost
      port: 8500
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ****************************************************************************************************************************************************************************************************************************
    username: root
    password:
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev, faker
    enabled: true
  jpa:
    hibernate:
      ddl-auto: update
  cache:
    type: jcache
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false

server:
  port: 8085
  # make sure requests the proxy uri instead of the server one
  forward-headers-strategy: native

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    redis: # Redis configuration
      expiration: 3600 # By default objects stay 1 hour (in seconds) in the cache
      server: redis://localhost:6379
      cluster: false
      # server: redis://localhost:6379,redis://localhost:16379,redis://localhost:26379
      # cluster: true
  # CORS is disabled by default on microservices, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  # cors:
  #   allowed-origins: "http://localhost:9000,https://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count"
  #   allow-credentials: true
  #   max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: YzU3OGViYWI3NzBjNzAxMTM1MDNhNzczZTZmZTJiNzk1Y2ZkMzUxNWM0ODIyMzA5NmU3MjA0M2RjNjUwYTAxYTRhYzFkMzg4YTFmOTVjYzJlZTY0YWY3ZjEzNjQ0NWQ3ODU4NzczZTY1MzY0MjQzM2NiNDc2NDMzNWRiNDk4NWE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

temporal:
  server:
    host: localhost
    port: 7233
  task-queue: AiTaskQueue

# RAG Configuration for Development
whiskerguard:
  ai:
    rag:
      enabled: false # 开发环境启用 RAG
      default-tenant-id: 1
      top-k: 3
      distance-metric: cosine
      min-score: 0.6
      fast-fail: true # 启用快速失败模式，避免用户等待
    claude: # Claude AI 配置
      api-url: https://api.anthropic.com
      api-key: ************************************************************************************************************
      model: claude-3-opus-20240229
      path: /v1/messages
      max-tokens: 4000
      temperature: 0.7

# Feign 客户端配置优化
feign:
  circuitbreaker:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 30000
      whiskerguardretrievalservice:
        connectTimeout: 3000 # 连接超时 3 秒 - 减少连接等待时间
        readTimeout: 8000 # 读取超时 8 秒 - 大幅减少读取超时，避免用户感觉卡死
        loggerLevel: basic

# Resilience4j 熔断器配置 - 针对 RAG 服务和 AI 服务优化
resilience4j:
  circuitbreaker:
    instances:
      rag-service:
        registerHealthIndicator: true
        slidingWindowSize: 10 # 滑动窗口大小
        minimumNumberOfCalls: 5 # 最小调用次数
        permittedNumberOfCallsInHalfOpenState: 3 # 半开状态允许的调用次数
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s # 熔断器打开状态持续时间
        failureRateThreshold: 50 # 失败率阈值 50%
        eventConsumerBufferSize: 10
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - feign.FeignException.ServiceUnavailable
          - feign.FeignException.InternalServerError
          - feign.RetryableException
          - java.util.concurrent.TimeoutException
      # 通义千问 API 熔断器配置
      qwen:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - org.springframework.web.reactive.function.client.WebClientResponseException
          - java.util.concurrent.TimeoutException
      # 通义千问流式调用熔断器配置
      qwen-stream:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - org.springframework.web.reactive.function.client.WebClientResponseException
          - java.util.concurrent.TimeoutException
    retry:
      instances:
        rag-service:
          maxAttempts: 2 # 最多重试 2 次（总共 3 次调用）
          waitDuration: 500ms # 重试间隔 500ms
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - feign.RetryableException
            - java.util.concurrent.TimeoutException
        # 通义千问 API 重试配置
        qwen:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
        # Kimi API 重试配置 - 开发环境
        kimi:
          maxAttempts: 3
          waitDuration: 3000ms  # 开发环境稍短的等待时间
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # 豆包 API 重试配置 - 开发环境
        doubao:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # Claude API 重试配置 - 开发环境
        claude:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # DeepSeek API 重试配置 - 开发环境
        deepseek:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # ChatGPT API 重试配置 - 开发环境
        chatgpt:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # LaWGPT API 重试配置 - 开发环境
        lawgpt:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
        # ChatLaw API 重试配置 - 开发环境
        chatlaw:
          maxAttempts: 3
          waitDuration: 2000ms
          exponentialBackoffMultiplier: 2.0
          retryExceptions:
            - java.net.ConnectException
            - java.net.SocketTimeoutException
            - org.springframework.web.reactive.function.client.WebClientRequestException
            - org.springframework.web.reactive.function.client.WebClientResponseException$TooManyRequests
            - java.util.concurrent.TimeoutException
            - java.lang.RuntimeException
  # 通义千问 API 限流配置
  bulkhead:
    instances:
      qwen:
        maxConcurrentCalls: 10
        maxWaitDuration: 5000ms
      qwen-stream:
        maxConcurrentCalls: 5
        maxWaitDuration: 3000ms
